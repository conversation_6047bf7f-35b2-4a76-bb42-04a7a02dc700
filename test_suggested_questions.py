#!/usr/bin/env python3
"""
Test script to verify suggested questions functionality
"""
import sys
import json
import requests
import time
from datetime import datetime

# Add current directory to path
sys.path.append('.')

def test_suggested_questions_endpoint():
    """
    Test the suggested questions test endpoint
    """
    print("TESTING SUGGESTED QUESTIONS ENDPOINT")
    print("=" * 60)

    base_url = "http://127.0.0.1:8000"

    try:
        response = requests.get(f"{base_url}/test_suggested_questions", timeout=30)
        if response.status_code == 200:
            print("✅ Suggested questions test endpoint responded successfully")
            result = response.json()
            print(f"Response: {result}")
            print("Check server logs for detailed test results")
        else:
            print(f"❌ Test endpoint failed: {response.status_code}")
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"❌ Error calling test endpoint: {e}")

def test_suggested_questions_flow():
    """
    Test the suggested questions functionality with simple queries
    """
    print("\nSUGGESTED QUESTIONS FLOW TEST")
    print("=" * 60)

    base_url = "http://127.0.0.1:8000"

    # Test data
    test_user_id = "64fa2c8b9a1d3b0a12345678"
    test_chat_id = "64fa2c8b9a1d3b0a12345690"  # New chat for clean test

    print(f"Test User ID: {test_user_id}")
    print(f"Test Chat ID: {test_chat_id}")

    # Test with simple queries that don't require complex LLM processing
    test_queries = [
        "hello",  # Should trigger greeting response
        "What is machine learning?",
        "How does Python work?"
    ]
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n📝 Test {i}: Testing query: '{query}'")
        
        query_request = {
            "query": query,
            "mode": "agentic",
            "host": "groq",
            "model": "llama-3.3-70b-versatile",
            "api_key": "gsk_test_key",
            "user_id": test_user_id,
            "chat_id": test_chat_id
        }
        
        try:
            response = requests.post(f"{base_url}/query", json=query_request, stream=True, timeout=60)
            if response.status_code == 200:
                print("✅ Query sent successfully")
                
                # Read the streaming response
                full_response = ""
                suggested_questions_found = False
                suggested_questions = []
                
                for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                    if chunk:
                        full_response += chunk
                        
                        # Check if we've reached the suggested questions section
                        if "Suggested Questions:" in chunk:
                            suggested_questions_found = True
                            print("✅ Found 'Suggested Questions:' section")
                        
                        # Extract suggested questions if we're in that section
                        if suggested_questions_found:
                            lines = chunk.split('\n')
                            for line in lines:
                                line = line.strip()
                                if line and (line.startswith('1.') or line.startswith('2.') or line.startswith('3.')):
                                    suggested_questions.append(line)
                
                print(f"Response length: {len(full_response)} characters")
                
                # Verify suggested questions
                if suggested_questions_found:
                    print(f"✅ Suggested questions section found")
                    print(f"Found {len(suggested_questions)} suggested questions:")
                    for sq in suggested_questions:
                        print(f"  {sq}")
                    
                    if len(suggested_questions) == 3:
                        print("✅ Correct number of suggested questions (3)")
                    else:
                        print(f"⚠️ Expected 3 suggested questions, found {len(suggested_questions)}")
                else:
                    print("❌ No suggested questions section found")
                
                # Check if Chat ID is present
                if f"Chat ID: {test_chat_id}" in full_response:
                    print("✅ Chat ID found in response")
                else:
                    print("❌ Chat ID not found in response")
                
                # Check response structure
                sections = ["Source:", "Documents:", "Chat ID:", "Suggested Questions:"]
                for section in sections:
                    if section in full_response:
                        print(f"✅ {section} section found")
                    else:
                        print(f"❌ {section} section missing")
                
            else:
                print(f"❌ Query failed: {response.status_code}")
                print(f"Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Error sending query: {e}")
        
        time.sleep(2)  # Wait between requests
        print("-" * 40)
    
    print(f"\n✅ Suggested questions test completed!")
    print("=" * 60)

def test_rag_query_suggested_questions():
    """
    Test suggested questions with RAG query endpoint
    """
    print("\nTesting RAG Query Suggested Questions...")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    # Test data
    test_user_id = "64fa2c8b9a1d3b0a12345678"
    test_chat_id = "64fa2c8b9a1d3b0a12345691"  # New chat for RAG test
    
    rag_query_request = {
        "query": "What is deep learning?",
        "mode": "rag",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "gsk_test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id,
        "collections": ["Gen AI"]
    }
    
    try:
        response = requests.post(f"{base_url}/rag_query", json=rag_query_request, stream=True, timeout=60)
        if response.status_code == 200:
            print("✅ RAG query sent successfully")
            
            # Read the streaming response
            full_response = ""
            suggested_questions_found = False
            
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
                    
                    if "Suggested Questions:" in chunk:
                        suggested_questions_found = True
            
            print(f"RAG response length: {len(full_response)} characters")
            
            if suggested_questions_found:
                print("✅ Suggested questions found in RAG response")
            else:
                print("❌ No suggested questions found in RAG response")
            
            # Check for key sections
            if "Chat ID:" in full_response:
                print("✅ Chat ID found in RAG response")
            else:
                print("❌ Chat ID not found in RAG response")
                
        else:
            print(f"❌ RAG query failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error with RAG query: {e}")

def test_suggested_questions_format():
    """
    Test the format of suggested questions
    """
    print("\nTesting Suggested Questions Format...")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    test_user_id = "64fa2c8b9a1d3b0a12345678"
    test_chat_id = "64fa2c8b9a1d3b0a12345692"
    
    query_request = {
        "query": "What is Python programming?",
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "gsk_test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=query_request, stream=True, timeout=60)
        if response.status_code == 200:
            print("✅ Format test query sent successfully")
            
            full_response = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
            
            # Extract suggested questions section
            if "Suggested Questions:" in full_response:
                lines = full_response.split('\n')
                in_suggested_section = False
                suggested_lines = []
                
                for line in lines:
                    if "Suggested Questions:" in line:
                        in_suggested_section = True
                        continue
                    elif in_suggested_section:
                        line = line.strip()
                        if line and (line.startswith('1.') or line.startswith('2.') or line.startswith('3.')):
                            suggested_lines.append(line)
                        elif line == "" or not line.startswith(('1.', '2.', '3.')):
                            # End of suggested questions section
                            break
                
                print(f"Extracted suggested questions:")
                for line in suggested_lines:
                    print(f"  {line}")
                    
                    # Check format
                    if len(line.split()) <= 12:  # Check if it's concise (max 12 words)
                        print(f"    ✅ Concise (≤12 words)")
                    else:
                        print(f"    ⚠️ Too long ({len(line.split())} words)")
                
                if len(suggested_lines) == 3:
                    print("✅ Correct format: exactly 3 questions")
                else:
                    print(f"❌ Incorrect format: {len(suggested_lines)} questions instead of 3")
            else:
                print("❌ No suggested questions section found")
                
        else:
            print(f"❌ Format test failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error in format test: {e}")

if __name__ == "__main__":
    print("SUGGESTED QUESTIONS TESTING SUITE")
    print("=" * 60)

    # Test the endpoint first
    test_suggested_questions_endpoint()

    # Test main functionality
    test_suggested_questions_flow()

    # Test RAG endpoint
    test_rag_query_suggested_questions()

    # Test format
    test_suggested_questions_format()

    print("\nAll suggested questions tests completed!")
    print("=" * 60)
