#!/usr/bin/env python3
"""
Test script to verify follow-up question continuation functionality
"""
import sys
import json
import requests
import time
from datetime import datetime

# Add current directory to path
sys.path.append('.')

def test_followup_continuation_flow():
    """
    Test the complete follow-up continuation flow
    """
    print("FOLLOW-UP CONTINUATION FLOW TEST")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    # Test data
    test_user_id = "64fa2c8b9a1d3b0a12345678"  # Use a consistent test user ID
    test_chat_id = "64fa2c8b9a1d3b0a12345679"  # Use a consistent test chat ID
    
    print(f"Test User ID: {test_user_id}")
    print(f"Test Chat ID: {test_chat_id}")
    
    # Step 1: Send initial query that should generate a follow-up
    print(f"\n📝 Step 1: Sending initial query...")
    
    initial_query = {
        "query": "What is machine learning?",
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "gsk_test_key",  # Use test key
        "user_id": test_user_id,
        "chat_id": test_chat_id
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=initial_query, stream=True)
        if response.status_code == 200:
            print("✅ Initial query sent successfully")
            # Read the streaming response
            full_response = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
            print(f"Response preview: {full_response[:100]}...")
        else:
            print(f"❌ Initial query failed: {response.status_code}")
            print(f"Error: {response.text}")
            return
    except Exception as e:
        print(f"❌ Error sending initial query: {e}")
        return
    
    # Wait a moment for storage
    time.sleep(2)
    
    # Step 2: Check chat history to see if follow-up was stored
    print(f"\n📖 Step 2: Checking chat history...")
    
    try:
        history_response = requests.get(f"{base_url}/chat_history/{test_user_id}/{test_chat_id}")
        if history_response.status_code == 200:
            history_data = history_response.json()
            messages = history_data.get('messages', [])
            
            print(f"Found {len(messages)} messages in history")
            
            # Look for assistant message with follow-up
            assistant_messages = [msg for msg in messages if msg.get('role') == 'assistant']
            if assistant_messages:
                latest_assistant = assistant_messages[-1]
                follow_up = latest_assistant.get('metadata', {}).get('follow_up')
                print(f"Latest assistant message follow-up: {follow_up}")
                
                if follow_up:
                    print("✅ Follow-up question found in chat history")
                else:
                    print("❌ No follow-up question found in chat history")
            else:
                print("❌ No assistant messages found in history")
        else:
            print(f"❌ Failed to get chat history: {history_response.status_code}")
    except Exception as e:
        print(f"❌ Error getting chat history: {e}")
    
    # Step 3: Send affirmative response to trigger follow-up continuation
    print(f"\n🔄 Step 3: Sending affirmative response...")
    
    affirmative_query = {
        "query": "yes",  # This should trigger follow-up continuation
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "gsk_test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=affirmative_query, stream=True)
        if response.status_code == 200:
            print("✅ Affirmative response sent successfully")
            # Read the streaming response
            full_response = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
            print(f"Response preview: {full_response[:100]}...")
        else:
            print(f"❌ Affirmative response failed: {response.status_code}")
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"❌ Error sending affirmative response: {e}")
    
    # Step 4: Test with different affirmative responses
    print(f"\n🎯 Step 4: Testing different affirmative responses...")
    
    affirmative_responses = ["ok", "go ahead", "tell me more", "sure"]
    
    for response_text in affirmative_responses:
        print(f"\nTesting: '{response_text}'")
        
        test_query = {
            "query": response_text,
            "mode": "agentic",
            "host": "groq",
            "model": "llama-3.3-70b-versatile",
            "api_key": "gsk_test_key",
            "user_id": test_user_id,
            "chat_id": test_chat_id
        }
        
        try:
            response = requests.post(f"{base_url}/query", json=test_query, stream=True)
            if response.status_code == 200:
                print(f"✅ '{response_text}' processed successfully")
            else:
                print(f"❌ '{response_text}' failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Error with '{response_text}': {e}")
        
        time.sleep(1)  # Small delay between requests
    
    # Step 5: Test with non-affirmative response
    print(f"\n🚫 Step 5: Testing non-affirmative response...")
    
    non_affirmative_query = {
        "query": "What is Python programming?",  # This should NOT trigger follow-up continuation
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "gsk_test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=non_affirmative_query, stream=True)
        if response.status_code == 200:
            print("✅ Non-affirmative query processed successfully")
            print("This should be treated as a new question, not follow-up continuation")
        else:
            print(f"❌ Non-affirmative query failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error with non-affirmative query: {e}")
    
    print(f"\n✅ Follow-up continuation flow test completed!")
    print("=" * 60)

def test_endpoint():
    """Test the test endpoint"""
    print("\nTesting the test endpoint...")
    
    try:
        response = requests.get("http://127.0.0.1:8000/test_followup_continuation")
        if response.status_code == 200:
            print("✅ Test endpoint responded successfully")
            print("Check server logs for detailed test results")
        else:
            print(f"❌ Test endpoint failed: {response.status_code}")
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"❌ Error calling test endpoint: {e}")

if __name__ == "__main__":
    print("FOLLOW-UP CONTINUATION TESTING SUITE")
    print("=" * 60)
    
    # Test the endpoint first
    test_endpoint()
    
    # Then test the full flow
    test_followup_continuation_flow()
    
    print("\nAll tests completed!")
    print("=" * 60)
