#!/usr/bin/env python3
"""
Test script to verify LLM-based suggested questions extraction functionality
"""
import sys
import json
import requests
import time
from datetime import datetime

# Add current directory to path
sys.path.append('.')

def test_extraction_functionality():
    """
    Test the extraction functionality directly
    """
    print("LLM SUGGESTED QUESTIONS EXTRACTION TEST")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    # Test the extraction endpoint
    try:
        response = requests.get(f"{base_url}/test_suggested_questions", timeout=30)
        if response.status_code == 200:
            print("✅ Extraction test endpoint responded successfully")
            result = response.json()
            print(f"Response: {result}")
            print("Check server logs for detailed extraction test results")
        else:
            print(f"❌ Extraction test endpoint failed: {response.status_code}")
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"❌ Error calling extraction test endpoint: {e}")

def test_llm_response_with_suggestions():
    """
    Test with a real query that should generate LLM responses with suggested questions
    """
    print("\nTESTING REAL LLM RESPONSES WITH SUGGESTED QUESTIONS")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    # Test data
    test_user_id = "64fa2c8b9a1d3b0a12345678"
    test_chat_id = "64fa2c8b9a1d3b0a12345700"  # New chat for clean test
    
    print(f"Test User ID: {test_user_id}")
    print(f"Test Chat ID: {test_chat_id}")
    
    # Test with a simple greeting first (should work without LLM)
    print(f"\n📝 Test 1: Testing with greeting (no LLM call)...")
    
    greeting_query = {
        "query": "hello",
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=greeting_query, stream=True, timeout=30)
        if response.status_code == 200:
            print("✅ Greeting query sent successfully")
            
            full_response = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
            
            print(f"Response length: {len(full_response)} characters")
            
            # Check for suggested questions section
            if "Suggested Questions:" in full_response:
                print("✅ Suggested Questions section found")
                
                # Extract suggested questions
                lines = full_response.split('\n')
                in_suggested = False
                suggested_count = 0
                
                for line in lines:
                    if "Suggested Questions:" in line:
                        in_suggested = True
                        print("Found suggested questions:")
                    elif in_suggested and line.strip():
                        if line.strip().startswith(('1.', '2.', '3.')):
                            print(f"  {line.strip()}")
                            suggested_count += 1
                        elif not line.strip().startswith(('1.', '2.', '3.')):
                            break
                
                if suggested_count == 3:
                    print("✅ Correct number of suggested questions (3)")
                else:
                    print(f"⚠️ Expected 3 suggested questions, found {suggested_count}")
            else:
                print("❌ No Suggested Questions section found")
            
            # Check for Chat ID
            if f"Chat ID: {test_chat_id}" in full_response:
                print("✅ Chat ID found in response")
            else:
                print("❌ Chat ID not found in response")
                
        else:
            print(f"❌ Greeting query failed: {response.status_code}")
            print(f"Error: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Error with greeting query: {e}")
    
    print("-" * 40)

def test_rag_endpoint_suggestions():
    """
    Test suggested questions with RAG endpoint
    """
    print("\nTESTING RAG ENDPOINT SUGGESTED QUESTIONS")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    test_user_id = "64fa2c8b9a1d3b0a12345678"
    test_chat_id = "64fa2c8b9a1d3b0a12345701"  # New chat for RAG test
    
    rag_query = {
        "query": "hello",  # Simple query to avoid LLM issues
        "mode": "rag",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id,
        "collections": ["Gen AI"]
    }
    
    try:
        response = requests.post(f"{base_url}/rag_query", json=rag_query, stream=True, timeout=30)
        if response.status_code == 200:
            print("✅ RAG query sent successfully")
            
            full_response = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
            
            print(f"RAG response length: {len(full_response)} characters")
            
            # Check for suggested questions
            if "Suggested Questions:" in full_response:
                print("✅ Suggested questions found in RAG response")
                
                # Count suggested questions
                lines = full_response.split('\n')
                suggested_count = 0
                for line in lines:
                    if line.strip().startswith(('1.', '2.', '3.')) and "Suggested Questions:" in full_response:
                        suggested_count += 1
                
                print(f"Found {suggested_count} suggested questions in RAG response")
            else:
                print("❌ No suggested questions found in RAG response")
            
            # Check for Chat ID
            if "Chat ID:" in full_response:
                print("✅ Chat ID found in RAG response")
            else:
                print("❌ Chat ID not found in RAG response")
                
        else:
            print(f"❌ RAG query failed: {response.status_code}")
            print(f"Error: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Error with RAG query: {e}")

def test_response_structure():
    """
    Test the overall response structure
    """
    print("\nTESTING RESPONSE STRUCTURE")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    test_user_id = "64fa2c8b9a1d3b0a12345678"
    test_chat_id = "64fa2c8b9a1d3b0a12345702"
    
    query_request = {
        "query": "hello",
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=query_request, stream=True, timeout=30)
        if response.status_code == 200:
            print("✅ Structure test query sent successfully")
            
            full_response = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
            
            # Check response structure order
            sections = ["Source:", "Documents:", "Chat ID:", "Suggested Questions:"]
            section_positions = {}
            
            for section in sections:
                pos = full_response.find(section)
                if pos != -1:
                    section_positions[section] = pos
                    print(f"✅ {section} found at position {pos}")
                else:
                    print(f"❌ {section} not found")
            
            # Check if sections are in correct order
            if len(section_positions) >= 3:
                positions = list(section_positions.values())
                if positions == sorted(positions):
                    print("✅ Sections are in correct order")
                else:
                    print("⚠️ Sections may not be in expected order")
            
            # Check if Suggested Questions comes after Chat ID
            if "Chat ID:" in section_positions and "Suggested Questions:" in section_positions:
                if section_positions["Suggested Questions:"] > section_positions["Chat ID:"]:
                    print("✅ Suggested Questions appears after Chat ID")
                else:
                    print("❌ Suggested Questions appears before Chat ID")
                    
        else:
            print(f"❌ Structure test failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error in structure test: {e}")

if __name__ == "__main__":
    print("LLM-BASED SUGGESTED QUESTIONS TESTING SUITE")
    print("=" * 60)
    
    # Test extraction functionality
    test_extraction_functionality()
    
    # Test with real queries
    test_llm_response_with_suggestions()
    
    # Test RAG endpoint
    test_rag_endpoint_suggestions()
    
    # Test response structure
    test_response_structure()
    
    print("\nAll LLM suggested questions tests completed!")
    print("=" * 60)
