# Follow-up Question Chaining Fix

## Problem Identified

The original implementation had a critical issue with follow-up question chaining:

1. **First Query**: User asks "What is machine learning?"
2. **First Response**: LLM responds with answer + follow-up question A
3. **User says "yes"**: System correctly retrieves follow-up A and answers it + generates follow-up B
4. **User says "yes" again**: ❌ System retrieves follow-up A again (not follow-up B)

**Root Cause**: The system always retrieved the "latest" follow-up without considering which ones had already been used, causing infinite loops on the same follow-up question.

## Solution Implemented

### 1. **Follow-up Usage Tracking**
- Added `mark_follow_up_as_used()` function to mark follow-ups as consumed
- Added `is_follow_up_already_used()` function to check if a follow-up was already used
- Uses system marker messages to track usage in Redis

### 2. **Unused Follow-up Retrieval**
- Added `get_latest_unused_follow_up_from_redis()` function
- Only retrieves follow-up questions that haven't been used yet
- Enables proper chaining by skipping already-consumed follow-ups

### 3. **Enhanced Timestamp-based Selection**
- Improved follow-up retrieval to use timestamps for proper chronological ordering
- Ensures the most recent unused follow-up is selected

## How Chaining Works Now

### Scenario: Machine Learning Question Chain

1. **User**: "What is machine learning?"
2. **Assistant**: "ML is a subset of AI... 👉 Would you like me to explain more about ML algorithms?" 
   - Follow-up A stored: "Would you like me to explain more about ML algorithms?"

3. **User**: "yes"
   - System finds Follow-up A (unused)
   - Marks Follow-up A as used
   - Processes: "Would you like me to explain more about ML algorithms?"

4. **Assistant**: "ML algorithms include supervised, unsupervised... 👉 Would you like me to explain supervised learning specifically?"
   - Follow-up B stored: "Would you like me to explain supervised learning specifically?"

5. **User**: "yes" 
   - System skips Follow-up A (already used)
   - Finds Follow-up B (unused)
   - Marks Follow-up B as used
   - Processes: "Would you like me to explain supervised learning specifically?"

6. **Assistant**: "Supervised learning uses labeled data... 👉 Would you like examples of supervised learning algorithms?"
   - Follow-up C stored: "Would you like examples of supervised learning algorithms?"

7. **User**: "yes"
   - System skips Follow-ups A & B (already used)
   - Finds Follow-up C (unused)
   - Continues the chain...

## Key Functions Added/Modified

### `mark_follow_up_as_used(user_id, chat_id, follow_up_question)`
```python
# Creates a system marker message to track usage
marker_message = {
    "role": "system",
    "content": f"[FOLLOW_UP_USED] {follow_up_question}",
    "metadata": {
        "type": "follow_up_marker",
        "used_follow_up": follow_up_question,
        "used_at": datetime.now().isoformat()
    }
}
```

### `is_follow_up_already_used(user_id, chat_id, follow_up_question)`
```python
# Checks Redis for system marker messages indicating usage
for msg in messages:
    if (msg.get('role') == 'system' and 
        msg.get('metadata', {}).get('type') == 'follow_up_marker' and
        msg.get('metadata', {}).get('used_follow_up') == follow_up_question):
        return True
```

### `get_latest_unused_follow_up_from_redis(user_id, chat_id)`
```python
# Finds the most recent follow-up that hasn't been used yet
for msg in reversed(messages):
    if (msg.get('role') == 'assistant' and 
        msg.get('metadata', {}).get('follow_up')):
        follow_up = msg['metadata']['follow_up']
        
        if not is_follow_up_already_used(user_id, chat_id, follow_up):
            return follow_up  # Return first unused follow-up found
```

### `handle_follow_up_continuation()` - Enhanced
```python
# Get the latest UNUSED follow-up question (enables chaining)
follow_up_question = get_latest_unused_follow_up_from_redis(user_id, chat_id)

if follow_up_question:
    # Mark this follow-up as used to prevent reuse
    mark_follow_up_as_used(user_id, chat_id, follow_up_question)
    return True, follow_up_question
```

## Benefits of the Fix

### ✅ **Proper Chaining**
- Each "yes" moves to the next follow-up in the conversation
- No more infinite loops on the same follow-up question
- Natural conversation flow maintained

### ✅ **Usage Tracking**
- System remembers which follow-ups have been answered
- Prevents accidental reuse of old follow-up questions
- Clean separation between used and unused follow-ups

### ✅ **Chronological Order**
- Always processes follow-ups in the correct time sequence
- Most recent unused follow-up is selected first
- Maintains conversation context and flow

### ✅ **Fallback Support**
- Redis-first approach with MongoDB fallback still works
- Session restoration preserves follow-up chain state
- Robust error handling for edge cases

## Testing

### Test Scenarios Covered

1. **Basic Chaining**: Multiple "yes" responses in sequence
2. **Mixed Responses**: "yes", then new question, then "yes" again
3. **Different Affirmatives**: "ok", "go ahead", "tell me more", etc.
4. **Redis Expiration**: Chaining works even after session restoration
5. **Non-affirmative Interruption**: New questions break the chain appropriately

### Test Files
- `test_followup_chaining.py` - Comprehensive chaining tests
- `/test_followup_continuation` endpoint - Server-side testing

## Example Flow

```
User: "What is machine learning?"
Assistant: "ML is... 👉 Want to know about ML types?"

User: "yes"
Assistant: "ML types are... 👉 Want to know about supervised learning?"

User: "yes"  
Assistant: "Supervised learning is... 👉 Want examples of algorithms?"

User: "yes"
Assistant: "Examples include... 👉 Want to know about neural networks?"

User: "What is Python?"  # Breaks chain
Assistant: "Python is a programming language..."
```

## Configuration

### System Marker Format
```json
{
  "role": "system",
  "content": "[FOLLOW_UP_USED] Would you like me to explain more about...",
  "metadata": {
    "type": "follow_up_marker",
    "used_follow_up": "Would you like me to explain more about...",
    "used_at": "2024-01-01T12:00:00"
  }
}
```

### Redis Storage
- System markers stored alongside regular messages
- Same TTL as conversation messages
- Automatically cleaned up when session expires

## Performance Impact

- **Minimal Overhead**: Only adds system marker messages
- **Efficient Lookup**: Uses reverse iteration for recent messages
- **Memory Efficient**: Markers are small and temporary
- **No Breaking Changes**: Existing functionality preserved

The chaining fix ensures that follow-up questions create a natural, progressive conversation flow where each "yes" response moves the conversation forward to the next logical step, rather than repeating the same follow-up question indefinitely.
