#!/usr/bin/env python3
"""
Test script to verify follow-up question chaining functionality
"""
import sys
import json
import requests
import time
from datetime import datetime

# Add current directory to path
sys.path.append('.')

def test_followup_chaining_flow():
    """
    Test the complete follow-up chaining flow
    """
    print("FOLLOW-UP CHAINING FLOW TEST")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    # Test data
    test_user_id = "64fa2c8b9a1d3b0a12345678"
    test_chat_id = "64fa2c8b9a1d3b0a12345680"  # New chat for clean test
    
    print(f"Test User ID: {test_user_id}")
    print(f"Test Chat ID: {test_chat_id}")
    
    # Step 1: Send initial query about machine learning
    print(f"\n📝 Step 1: Sending initial query about machine learning...")
    
    initial_query = {
        "query": "What is machine learning?",
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "gsk_test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=initial_query, stream=True, timeout=30)
        if response.status_code == 200:
            print("✅ Initial ML query sent successfully")
            full_response = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
            print(f"Response preview: {full_response[:150]}...")
            
            # Check if response contains follow-up marker
            if "👉" in full_response:
                print("✅ Response contains follow-up question")
            else:
                print("⚠️ Response doesn't contain follow-up question")
        else:
            print(f"❌ Initial query failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Error sending initial query: {e}")
        return
    
    time.sleep(3)  # Wait for storage
    
    # Step 2: Send first "yes" to get first follow-up
    print(f"\n🔄 Step 2: Sending first 'yes' to continue with follow-up...")
    
    first_yes_query = {
        "query": "yes",
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "gsk_test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=first_yes_query, stream=True, timeout=30)
        if response.status_code == 200:
            print("✅ First 'yes' sent successfully")
            full_response = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
            print(f"First follow-up response preview: {full_response[:150]}...")
            
            # Check if this response also contains a follow-up
            if "👉" in full_response:
                print("✅ First follow-up response contains another follow-up question")
                first_followup_response = full_response
            else:
                print("⚠️ First follow-up response doesn't contain another follow-up")
                first_followup_response = full_response
        else:
            print(f"❌ First 'yes' failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Error sending first 'yes': {e}")
        return
    
    time.sleep(3)  # Wait for storage
    
    # Step 3: Send second "yes" to test chaining
    print(f"\n🔗 Step 3: Sending second 'yes' to test chaining...")
    
    second_yes_query = {
        "query": "yes",
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "gsk_test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=second_yes_query, stream=True, timeout=30)
        if response.status_code == 200:
            print("✅ Second 'yes' sent successfully")
            full_response = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
            print(f"Second follow-up response preview: {full_response[:150]}...")
            
            # Compare with first response to verify chaining
            if full_response != first_followup_response:
                print("✅ CHAINING WORKS! Second 'yes' produced different response")
            else:
                print("❌ CHAINING FAILED! Second 'yes' produced same response")
                
        else:
            print(f"❌ Second 'yes' failed: {response.status_code}")
            return
    except Exception as e:
        print(f"❌ Error sending second 'yes': {e}")
        return
    
    time.sleep(2)
    
    # Step 4: Check chat history to verify follow-up storage
    print(f"\n📖 Step 4: Checking chat history...")
    
    try:
        history_response = requests.get(f"{base_url}/chat_history/{test_user_id}/{test_chat_id}")
        if history_response.status_code == 200:
            history_data = history_response.json()
            messages = history_data.get('messages', [])
            
            print(f"Found {len(messages)} messages in history")
            
            # Count assistant messages with follow-ups
            assistant_with_followups = 0
            for msg in messages:
                if (msg.get('role') == 'assistant' and 
                    msg.get('metadata', {}).get('follow_up')):
                    assistant_with_followups += 1
                    follow_up = msg['metadata']['follow_up']
                    print(f"  Assistant message with follow-up: {follow_up[:50]}...")
            
            print(f"Found {assistant_with_followups} assistant messages with follow-ups")
            
            if assistant_with_followups >= 2:
                print("✅ Multiple follow-ups stored correctly")
            else:
                print("⚠️ Expected multiple follow-ups for chaining test")
                
        else:
            print(f"❌ Failed to get chat history: {history_response.status_code}")
    except Exception as e:
        print(f"❌ Error getting chat history: {e}")
    
    # Step 5: Test non-affirmative response
    print(f"\n🚫 Step 5: Testing non-affirmative response...")
    
    non_affirmative_query = {
        "query": "What is Python programming?",
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "gsk_test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=non_affirmative_query, stream=True, timeout=30)
        if response.status_code == 200:
            print("✅ Non-affirmative query processed successfully")
            print("This should be treated as a new question, not follow-up continuation")
        else:
            print(f"❌ Non-affirmative query failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error with non-affirmative query: {e}")
    
    print(f"\n✅ Follow-up chaining flow test completed!")
    print("=" * 60)

def test_different_affirmative_responses():
    """Test different affirmative responses for chaining"""
    print("\nTesting different affirmative responses...")
    
    base_url = "http://127.0.0.1:8000"
    test_user_id = "64fa2c8b9a1d3b0a12345678"
    test_chat_id = "64fa2c8b9a1d3b0a12345681"  # Another new chat
    
    affirmative_responses = ["ok", "go ahead", "tell me more", "sure", "explain"]
    
    # First, send an initial query to generate a follow-up
    initial_query = {
        "query": "What is artificial intelligence?",
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "gsk_test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=initial_query, stream=True, timeout=30)
        if response.status_code == 200:
            print("✅ Initial AI query sent for affirmative test")
        time.sleep(2)
    except Exception as e:
        print(f"❌ Error in initial query for affirmative test: {e}")
        return
    
    # Test each affirmative response
    for i, affirmative in enumerate(affirmative_responses):
        print(f"\nTesting affirmative response: '{affirmative}'")
        
        test_query = {
            "query": affirmative,
            "mode": "agentic",
            "host": "groq",
            "model": "llama-3.3-70b-versatile",
            "api_key": "gsk_test_key",
            "user_id": test_user_id,
            "chat_id": test_chat_id
        }
        
        try:
            response = requests.post(f"{base_url}/query", json=test_query, stream=True, timeout=30)
            if response.status_code == 200:
                print(f"✅ '{affirmative}' processed successfully")
            else:
                print(f"❌ '{affirmative}' failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Error with '{affirmative}': {e}")
        
        time.sleep(1)  # Small delay between requests

if __name__ == "__main__":
    print("FOLLOW-UP CHAINING TESTING SUITE")
    print("=" * 60)
    
    # Test the main chaining flow
    test_followup_chaining_flow()
    
    # Test different affirmative responses
    test_different_affirmative_responses()
    
    print("\nAll chaining tests completed!")
    print("=" * 60)
