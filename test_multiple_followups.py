#!/usr/bin/env python3
"""
Test script to verify multiple 👉 follow-up extraction functionality
"""
import sys
import json
import requests
import time
from datetime import datetime

# Add current directory to path
sys.path.append('.')

def test_extraction_endpoint():
    """
    Test the extraction functionality endpoint
    """
    print("MULTIPLE 👉 FOLLOW-UP EXTRACTION TEST")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    # Test the extraction endpoint
    try:
        response = requests.get(f"{base_url}/test_suggested_questions", timeout=30)
        if response.status_code == 200:
            print("✅ Multiple follow-up extraction test endpoint responded successfully")
            result = response.json()
            print(f"Response: {result}")
            print("Check server logs for detailed extraction test results")
        else:
            print(f"❌ Extraction test endpoint failed: {response.status_code}")
            print(f"Error: {response.text}")
    except Exception as e:
        print(f"❌ Error calling extraction test endpoint: {e}")

def test_direct_extraction():
    """
    Test the extraction functionality directly
    """
    print("\nDIRECT EXTRACTION TESTING")
    print("=" * 60)
    
    # Import the memory manager for direct testing
    try:
        from memory_manager2 import MemoryManager
        memory_manager = MemoryManager()
        
        # Test cases with multiple 👉 follow-ups
        test_responses = [
            """Machine learning is a subset of artificial intelligence.

👉 Would you like me to explain the different types of machine learning?
👉 Should I elaborate on how ML models are trained?
👉 Are you interested in popular ML frameworks?
👉 Would you like to explore real-world ML applications?""",
            
            """Python is a programming language.

👉 Would you like to learn about Python features?
👉 Should I show you Python installation steps?""",
            
            """This response has no follow-up questions."""
        ]
        
        for i, response in enumerate(test_responses, 1):
            print(f"\n--- Direct Test Case {i} ---")
            print(f"Response: {response[:50]}...")
            
            try:
                # Test all follow-ups extraction
                answer, all_followups = memory_manager.extract_all_follow_ups(response)
                print(f"All follow-ups extracted: {len(all_followups)}")
                for j, q in enumerate(all_followups, 1):
                    print(f"  👉 {j}: {q}")
                
                # Test main + suggestions extraction
                final_answer, main_followup, suggestions = memory_manager.extract_follow_up_and_suggestions(response)
                print(f"Main follow-up: {main_followup}")
                print(f"Suggested questions ({len(suggestions)}):")
                for j, q in enumerate(suggestions, 1):
                    print(f"  {j}. {q}")
                
                print(f"Final answer: {final_answer[:50]}...")
                
            except Exception as e:
                print(f"❌ Error in direct extraction: {e}")
                
    except ImportError as e:
        print(f"❌ Could not import memory manager: {e}")

def test_response_format():
    """
    Test the expected response format
    """
    print("\nRESPONSE FORMAT TESTING")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    test_user_id = "64fa2c8b9a1d3b0a12345678"
    test_chat_id = "64fa2c8b9a1d3b0a12345800"  # New chat for format test
    
    # Test with a simple greeting to avoid LLM complexity
    query_request = {
        "query": "hello",
        "mode": "agentic",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id
    }
    
    try:
        response = requests.post(f"{base_url}/query", json=query_request, stream=True, timeout=30)
        if response.status_code == 200:
            print("✅ Format test query sent successfully")
            
            full_response = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
            
            print(f"Response length: {len(full_response)} characters")
            
            # Analyze response structure
            lines = full_response.split('\n')
            
            # Look for key sections
            found_main_answer = False
            found_main_followup = False
            found_chat_id = False
            found_suggested_questions = False
            suggested_count = 0
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                if line and not line.startswith(('Source:', 'Documents:', 'Chat ID:', 'Suggested Questions:', '1.', '2.', '3.')):
                    if not found_main_answer:
                        found_main_answer = True
                        print(f"✅ Main answer found: {line[:50]}...")
                
                if line.startswith('👉'):
                    if not found_main_followup:
                        found_main_followup = True
                        print(f"✅ Main follow-up found: {line}")
                
                if line.startswith('Chat ID:'):
                    found_chat_id = True
                    print(f"✅ Chat ID found: {line}")
                
                if line == 'Suggested Questions:':
                    found_suggested_questions = True
                    print(f"✅ Suggested Questions section found")
                
                if found_suggested_questions and line.startswith(('1.', '2.', '3.')):
                    suggested_count += 1
                    print(f"  {line}")
            
            # Verify structure
            print(f"\nStructure Analysis:")
            print(f"  Main answer: {'✅' if found_main_answer else '❌'}")
            print(f"  Main follow-up (👉): {'✅' if found_main_followup else '❌'}")
            print(f"  Chat ID: {'✅' if found_chat_id else '❌'}")
            print(f"  Suggested Questions section: {'✅' if found_suggested_questions else '❌'}")
            print(f"  Suggested questions count: {suggested_count}")
            
            if found_suggested_questions and suggested_count <= 3:
                print("✅ Response format is correct")
            else:
                print("⚠️ Response format may need adjustment")
                
        else:
            print(f"❌ Format test failed: {response.status_code}")
            print(f"Error: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Error in format test: {e}")

def test_rag_endpoint_format():
    """
    Test the RAG endpoint format
    """
    print("\nRAG ENDPOINT FORMAT TESTING")
    print("=" * 60)
    
    base_url = "http://127.0.0.1:8000"
    
    test_user_id = "64fa2c8b9a1d3b0a12345678"
    test_chat_id = "64fa2c8b9a1d3b0a12345801"  # New chat for RAG test
    
    rag_query = {
        "query": "hello",
        "mode": "rag",
        "host": "groq",
        "model": "llama-3.3-70b-versatile",
        "api_key": "test_key",
        "user_id": test_user_id,
        "chat_id": test_chat_id,
        "collections": ["Gen AI"]
    }
    
    try:
        response = requests.post(f"{base_url}/rag_query", json=rag_query, stream=True, timeout=30)
        if response.status_code == 200:
            print("✅ RAG format test query sent successfully")
            
            full_response = ""
            for chunk in response.iter_content(chunk_size=1024, decode_unicode=True):
                if chunk:
                    full_response += chunk
            
            print(f"RAG response length: {len(full_response)} characters")
            
            # Check for suggested questions in RAG response
            if "Suggested Questions:" in full_response:
                print("✅ Suggested questions found in RAG response")
                
                # Count suggested questions
                lines = full_response.split('\n')
                suggested_count = 0
                for line in lines:
                    if line.strip().startswith(('1.', '2.', '3.')) and "Suggested Questions:" in full_response:
                        suggested_count += 1
                
                print(f"Found {suggested_count} suggested questions in RAG response")
                
                if suggested_count <= 3:
                    print("✅ RAG suggested questions format is correct")
                else:
                    print(f"⚠️ Too many suggested questions in RAG: {suggested_count}")
            else:
                print("❌ No suggested questions found in RAG response")
                
        else:
            print(f"❌ RAG format test failed: {response.status_code}")
            print(f"Error: {response.text[:200]}...")
            
    except Exception as e:
        print(f"❌ Error in RAG format test: {e}")

if __name__ == "__main__":
    print("MULTIPLE 👉 FOLLOW-UP TESTING SUITE")
    print("=" * 60)
    
    # Test extraction endpoint
    test_extraction_endpoint()
    
    # Test direct extraction
    test_direct_extraction()
    
    # Test response format
    test_response_format()
    
    # Test RAG endpoint format
    test_rag_endpoint_format()
    
    print("\nAll multiple follow-up tests completed!")
    print("=" * 60)
