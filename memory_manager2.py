#!/usr/bin/env python3
"""
Memory Manager - Short-term (Redis) and Long-term (MongoDB) Memory
Provides context management for the Multi-Agent RAG system
"""
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import redis
import ssl
from pymongo import MongoClient
from bson.objectid import ObjectId
import os
from dotenv import load_dotenv
from datetime import timedelta
import re

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("MemoryManager")

class MemoryManager:
    """
    Memory manager that handles both short-term (Redis) and long-term (MongoDB) memory
    Integrates with existing Redis and MongoDB setup from main2.py
    """
    
    def __init__(self, redis_client=None, mongo_client=None, mongo_db=None, mongo_collection=None):
        # Redis configuration
        self.redis_client = redis_client
        self.session_ttl = int(os.getenv("SESSION_TTL", 900))  # 15 minutes
        
        # MongoDB configuration
        self.mongo_client = mongo_client
        self.mongo_db = mongo_db
        self.mongo_collection = mongo_collection
        
        logger.info("Memory Manager initialized")
    
    def _validate_object_id(self, id_str: str, id_name: str) -> ObjectId:
        """Validate and convert string to ObjectId."""
        try:
            return ObjectId(id_str)
        except Exception as e:
            logger.error(f"Invalid {id_name}: {id_str}. Error: {e}")
            raise ValueError(f"Invalid {id_name}: {id_str}")
    
    # Follow-up Question Management Functions
    def extract_all_follow_ups(self, response_text: str) -> tuple[str, List[str]]:
        """
        Extract all follow-up questions from LLM response using regex pattern matching.

        Args:
            response_text: The full LLM response text

        Returns:
            Tuple containing (cleaned_answer, follow_up_questions_list)
            - answer: The response text with all follow-up questions removed
            - follow_up_questions_list: List of extracted follow-up questions
        """
        try:
            # Pattern to match all follow-up questions starting with 👉
            pattern = r'👉\s*(.+?)(?=\n👉|\n\n|\n$|$)'
            matches = re.findall(pattern, response_text, re.IGNORECASE | re.DOTALL)

            follow_up_questions = []
            if matches:
                for match in matches:
                    follow_up = match.strip()
                    # Clean up the follow-up question
                    follow_up = re.sub(r'^["\'\s]+|["\'\s]+$', '', follow_up)  # Remove quotes and extra spaces
                    if follow_up:
                        follow_up_questions.append(follow_up)

                # Remove all follow-up questions from the response text
                answer = re.sub(r'👉\s*.+?(?=\n👉|\n\n|\n$|$)', '', response_text, flags=re.IGNORECASE | re.DOTALL).strip()

                logger.info(f"📝 Extracted {len(follow_up_questions)} follow-up questions")
                for i, q in enumerate(follow_up_questions, 1):
                    logger.debug(f"  {i}. {q}")

                return answer, follow_up_questions

            logger.debug("No follow-up questions found in response")
            return response_text.strip(), []
        except Exception as e:
            logger.error(f"Error extracting follow-up questions: {e}")
            return response_text.strip(), []

    def extract_follow_up(self, response_text: str) -> tuple[str, Optional[str]]:
        """
        Extract the first follow-up question from LLM response (for backward compatibility).

        Args:
            response_text: The full LLM response text

        Returns:
            Tuple containing (cleaned_answer, follow_up_question)
            - answer: The response text with all follow-up questions removed
            - follow_up_question: The first extracted follow-up question or None if not found
        """
        try:
            answer, follow_up_questions = self.extract_all_follow_ups(response_text)

            if follow_up_questions:
                # Return the first follow-up question for backward compatibility
                return answer, follow_up_questions[0]
            else:
                return answer, None

        except Exception as e:
            logger.error(f"Error extracting follow_up question: {e}")
            return response_text.strip(), None



    def extract_follow_up_and_suggestions(self, response_text: str) -> tuple[str, Optional[str], List[str]]:
        """
        Extract main follow-up question and suggested questions from LLM response.
        Uses the first 👉 question as main follow-up and remaining 👉 questions as suggestions.

        Args:
            response_text: The full LLM response text

        Returns:
            Tuple containing (cleaned_answer, follow_up_question, suggested_questions_list)
            - cleaned_answer: The response text with all follow-up questions removed
            - follow_up_question: The first follow-up question (for main follow-up)
            - suggested_questions_list: The remaining follow-up questions (for suggestions)
        """
        try:
            # Extract all follow-up questions using 👉 pattern
            final_answer, all_follow_ups = self.extract_all_follow_ups(response_text)

            if all_follow_ups:
                # Use the first question as the main follow-up
                main_follow_up = all_follow_ups[0]

                # Use the remaining questions as suggested questions (up to 3)
                suggested_questions = all_follow_ups[1:4]  # Take questions 2, 3, 4 (max 3)

                logger.info(f"📝 Extracted: main_follow_up='{main_follow_up}', {len(suggested_questions)} suggested questions")
                for i, q in enumerate(suggested_questions, 1):
                    logger.debug(f"  Suggested {i}: {q}")

                return final_answer, main_follow_up, suggested_questions
            else:
                logger.debug("No follow-up questions found in response")
                return final_answer, None, []

        except Exception as e:
            logger.error(f"Error extracting follow-up and suggestions: {e}")
            return response_text.strip(), None, []
    
    def store_interaction(self, query: str, response: str, chat_id: str, user_id: str, follow_up: Optional[str] = None, 
                         metadata: Dict = None):
        """
        Store complete interaction in both short-term and long-term memory
        
        Args:
            query: User query
            response: System response
            chat_id: Chat session identifier (string, will be converted to ObjectId)
            user_id: User identifier (string, will be converted to ObjectId)
            follow_up: Optional pre-extracted follow-up question (not used here)
            metadata: Additional metadata
        """
        current_time = datetime.now()
        chat_id_obj = self._validate_object_id(chat_id, "chat_id")
        user_id_obj = self._validate_object_id(user_id, "user_id")
        
        # Create user message with chat_id and user_id in metadata
        user_metadata = metadata.copy() if metadata else {}
        user_metadata.update({"chat_id": str(chat_id_obj), "user_id": str(user_id_obj)})
        user_message = {
            "id": str(ObjectId()),
            "role": "user",
            "content": query,
            "timestamp": current_time,
            "metadata": user_metadata
        }

        # Do not extract follow-up here; defer to store_short_term_message
        answer = response
        assistant_metadata = metadata.copy() if metadata else {}
        assistant_metadata.update({
            "chat_id": str(chat_id_obj),
            "user_id": str(user_id_obj),
            "follow_up": None  # Will be updated after store_short_term_message
        })

        assistant_message = {
            "id": str(ObjectId()),
            "role": "assistant",
            "content": answer,
            "timestamp": current_time,
            "metadata": assistant_metadata
        }

        logger.debug(f"Prepared assistant message with metadata: {assistant_message['metadata']}")
        
        # Store in short-term memory (Redis) and get follow-up question
        follow_up = None
        if self.redis_client:
            try:
                session_key = f"session:{str(user_id_obj)}:{str(chat_id_obj)}"
                recent_threshold = (datetime.now() - timedelta(minutes=1))
                
                recent_msgs = self.redis_client.lrange(session_key, -10, -1)
                skip_redis = False
                for msg_str in recent_msgs:
                    try:
                        msg = json.loads(msg_str)
                        msg_timestamp_str = msg.get('timestamp')
                        if msg_timestamp_str:
                            msg_timestamp = datetime.fromisoformat(msg_timestamp_str)
                        else:
                            continue
                        if (msg.get('role') == 'user' and
                            msg.get('content') == query and
                            msg_timestamp > recent_threshold):
                            logger.info(f"Skipping near-duplicate Redis storage for query '{query}' in session {session_key}")
                            skip_redis = True
                            break
                    except json.JSONDecodeError:
                        continue

                if not skip_redis:
                    # Store in short-term memory (Redis) only if not duplicate
                    self.store_short_term_message(str(user_id_obj), str(chat_id_obj), user_message)
                    # Store assistant message and get follow-up
                    follow_up = self.store_short_term_message(str(user_id_obj), str(chat_id_obj), assistant_message)
            except Exception as e:
                logger.error(f"Error checking/storing in Redis: {e}")
                # Fallback: Store anyway if check fails
                self.store_short_term_message(str(user_id_obj), str(chat_id_obj), user_message)
                follow_up = self.store_short_term_message(str(user_id_obj), str(chat_id_obj), assistant_message)
        
        # Update assistant message metadata with follow-up for MongoDB
        assistant_message['metadata']['follow_up'] = follow_up
        logger.debug(f"Updated assistant message metadata with follow_up: {follow_up}")
        
        # Store in long-term memory (MongoDB) with atomic operation
        if self.mongo_collection is not None:
            try:
                recent_threshold = (datetime.now() - timedelta(minutes=1))
                
                # Enhanced check: Look for recent user message with same content (near-duplicate prevention)
                recent_duplicate = self.mongo_collection.find_one({
                    "chatId": chat_id_obj,
                    "user": user_id_obj,
                    "messages": {
                        "$elemMatch": {
                            "role": "user",
                            "content": query,
                            "timestamp": {"$gt": recent_threshold}
                        }
                    }
                })
                
                if recent_duplicate:
                    logger.info(f"Skipping near-duplicate MongoDB storage for query '{query}' in chat_id {chat_id} (recent match found)")
                    return
                
                # Existing strict check for exact timestamp duplicate
                exact_duplicate = self.mongo_collection.find_one({
                    "chatId": chat_id_obj,
                    "user": user_id_obj,
                    "messages": {
                        "$elemMatch": {
                            "role": "user",
                            "content": query,
                            "timestamp": current_time
                        }
                    }
                })
                
                if exact_duplicate:
                    logger.info(f"Skipping exact duplicate MongoDB storage for query '{query}' in chat_id {chat_id}")
                    return
    
                # Check if this is the first interaction and generate title
                session_doc = self.mongo_collection.find_one({"chatId": chat_id_obj, "user": user_id_obj})
                is_first = not session_doc or not session_doc.get('messages')
                title = None
                if is_first:
                    title = query[:50] + "..." if len(query) > 50 else query
                    logger.info(f"Generating title for new chat: {title}")

                # Prepare update document
                update_doc = {
                    "$push": {
                        "messages": {"$each": [user_message, assistant_message]}
                    },
                    "$set": {
                        "updatedAt": current_time,
                        "user": user_id_obj,
                        "createdAt": current_time
                    }
                }
                if title:
                    update_doc["$set"]["title"] = title

                # Use find_one_and_update with upsert to ensure atomicity
                self.mongo_collection.find_one_and_update(
                    {"chatId": chat_id_obj, "user": user_id_obj},
                    update_doc,
                    upsert=True
                )
                logger.debug(f"Stored interaction in MongoDB for chat_id {chat_id}, user {user_id}, messages: {user_message['id']}, {assistant_message['id']}")

                # If title was set, store it in Redis metadata as well
                if title and self.redis_client:
                    self.store_session_metadata(str(user_id_obj), str(chat_id_obj), {"title": title})

            except Exception as e:
                logger.error(f"Error storing interaction in MongoDB: {e}")

        logger.debug(f"Interaction stored for chat_id {chat_id}, user_id {user_id}")

    def get_short_term_context(self, user_id: str, chat_id: str, limit: int = 20) -> List[Dict]:
        """
        Get short-term context from Redis
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
            limit: Maximum number of messages to retrieve
            
        Returns:
            List of recent messages from Redis
        """
        if not self.redis_client:
            logger.debug("Redis not available, returning empty context")
            return []
        
        try:
            user_id_obj = self._validate_object_id(user_id, "user_id")
            chat_id_obj = self._validate_object_id(chat_id, "chat_id")
            session_key = f"session:{str(user_id_obj)}:{str(chat_id_obj)}"
            messages = self.redis_client.lrange(session_key, -limit, -1)
            
            if messages:
                # Extend TTL since we're accessing the session
                self.redis_client.expire(session_key, self.session_ttl)
                parsed_messages = []
                for msg in messages:
                    parsed_msg = json.loads(msg)
                    # Convert id to ObjectId
                    if parsed_msg.get('id') and isinstance(parsed_msg['id'], str):
                        parsed_msg['id'] = ObjectId(parsed_msg['id'])
                    # Convert timestamp to datetime
                    if parsed_msg.get('timestamp'):
                        parsed_msg['timestamp'] = datetime.fromisoformat(parsed_msg['timestamp'])
                    # Convert metadata IDs
                    if parsed_msg.get('metadata', {}).get('chat_id'):
                        parsed_msg['metadata']['chat_id'] = ObjectId(parsed_msg['metadata']['chat_id'])
                    if parsed_msg.get('metadata', {}).get('user_id'):
                        parsed_msg['metadata']['user_id'] = ObjectId(parsed_msg['metadata']['user_id'])
                    parsed_messages.append(parsed_msg)
                logger.debug(f"Retrieved {len(parsed_messages)} messages from Redis for session {session_key}")
                return parsed_messages
            
            logger.debug(f"No messages found in Redis for session {session_key}")
            return []
            
        except redis.ConnectionError as e:
            logger.error(f"Redis connection error: {e}")
            return []
        except Exception as e:
            logger.error(f"Error retrieving short-term context: {e}")
            return []

    def get_long_term_context(self, chat_id: str, user_id: str, limit: int = 20) -> List[Dict]:
        """
        Get long-term context from MongoDB

        Args:
            chat_id: Chat session identifier
            user_id: User identifier
            limit: Maximum number of messages to retrieve

        Returns:
            List of messages from MongoDB
        """
        if self.mongo_collection is None:
            logger.debug("MongoDB not available, returning empty context")
            return []
        
        try:
            chat_id_obj = self._validate_object_id(chat_id, "chat_id")
            user_id_obj = self._validate_object_id(user_id, "user_id")
            session_doc = self.mongo_collection.find_one({
                "chatId": chat_id_obj,
                "user": user_id_obj
            })
            
            if not session_doc:
                logger.debug(f"No session found in MongoDB for chat_id {chat_id}, user {user_id}")
                return []
            
            messages = session_doc.get('messages', [])
            # Convert message IDs to ObjectId if stored as strings
            for msg in messages:
                if msg.get('id') and isinstance(msg.get('id'), str):
                    msg['id'] = ObjectId(msg['id'])
                if msg.get('metadata', {}).get('chat_id') and isinstance(msg['metadata']['chat_id'], str):
                    msg['metadata']['chat_id'] = ObjectId(msg['metadata']['chat_id'])
                if msg.get('metadata', {}).get('user_id') and isinstance(msg['metadata']['user_id'], str):
                    msg['metadata']['user_id'] = ObjectId(msg['metadata']['user_id'])
            return messages[-limit:] if len(messages) > limit else messages
            
        except Exception as e:
            logger.error(f"Error retrieving long-term context: {e}")
            return []
    
    def get_context(self, chat_id: str, user_id: str, limit: int = 10) -> str:
        """
        Get context with strict session isolation and MongoDB fallback
        
        Args:
            chat_id: Chat session identifier
            user_id: User identifier
            limit: Maximum number of messages
        
        Returns:
            Session-specific formatted context string
        """
        try:
            chat_id_obj = self._validate_object_id(chat_id, "chat_id")
            user_id_obj = self._validate_object_id(user_id, "user_id")
            
            if not self.ensure_session_isolation(str(user_id_obj), str(chat_id_obj)):
                logger.warning(f"Session isolation failed for chat_id {chat_id}, user_id {user_id}")
            
            session_messages = self.get_session_specific_context(str(user_id_obj), str(chat_id_obj), limit)
            
            if session_messages:
                logger.debug(f"Using Redis context: {len(session_messages)} messages for chat_id {chat_id}, user_id {user_id}")
            else:
                session_messages = self.get_long_term_context(chat_id, user_id, limit)
                if session_messages:
                    logger.debug(f"Restoring {len(session_messages)} messages from MongoDB to Redis for chat_id {chat_id}, user_id {user_id}")
                    session_key = f"session:{str(user_id_obj)}:{str(chat_id_obj)}"
                    for msg in session_messages:
                        if str(msg.get('metadata', {}).get('user_id')) == str(user_id_obj):
                            self.store_short_term_message(str(user_id_obj), str(chat_id_obj), msg)
                else:
                    logger.debug(f"No context found for chat_id {chat_id}, user_id {user_id}")
                    return ""
            
            if not session_messages:
                return ""
            
            formatted_context = []
            for msg in session_messages:
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                if role and content and str(msg.get('metadata', {}).get('user_id')) == str(user_id_obj):
                    formatted_context.append(f"{role.capitalize()}: {content}")
            
            return "\n".join(formatted_context)
            
        except Exception as e:
            logger.error(f"Error getting session context: {e}")
            return ""

    def list_user_sessions(self, user_id: str) -> List[Dict]:
        """
        List all active sessions for a user
        
        Args:
            user_id: User identifier
        
        Returns:
            List of active sessions with metadata
        """
        if not self.redis_client:
            return []
        
        try:
            user_id_obj = self._validate_object_id(user_id, "user_id")
            pattern = f"session_meta:{str(user_id_obj)}:*"
            session_keys = self.redis_client.keys(pattern)
            
            sessions = []
            for key in session_keys:
                key_str = key.decode('utf-8') if isinstance(key, bytes) else key
                chat_id = key_str.split(':')[-1]
                try:
                    chat_id_obj = ObjectId(chat_id)
                except:
                    continue
                
                metadata = self.redis_client.hgetall(key)
                if metadata:
                    sessions.append({
                        'chat_id': str(chat_id_obj),
                        'user_id': str(user_id_obj),
                        'metadata': metadata,
                        'session_key': f"session:{str(user_id_obj)}:{str(chat_id_obj)}"
                    })
            
            logger.debug(f"Found {len(sessions)} active sessions for user {user_id}")
            return sessions
            
        except Exception as e:
            logger.error(f"Error listing user sessions: {e}")
            return []
    
    def store_short_term_message(self, user_id: str, chat_id: str, message: Dict) -> Optional[str]:
        """
        Store message in short-term memory (Redis) and return extracted follow-up for assistant messages
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
            message: Message dictionary
        
        Returns:
            Extracted follow-up question for assistant messages, None otherwise
        """
        if not self.redis_client:
            logger.debug("Redis not available, skipping short-term storage")
            return None
        
        try:
            user_id_obj = self._validate_object_id(user_id, "user_id")
            chat_id_obj = self._validate_object_id(chat_id, "chat_id")
            session_key = f"session:{str(user_id_obj)}:{str(chat_id_obj)}"
            
            message_copy = message.copy()
            follow_up = None

            # If it's an assistant message, extract follow-up
            if message_copy.get('role') == 'assistant':
                if 'metadata' not in message_copy:
                    message_copy['metadata'] = {}
                answer, follow_up = self.extract_follow_up(message_copy['content'])
                message_copy['content'] = answer
                message_copy['metadata']['follow_up'] = follow_up

            if isinstance(message_copy.get('timestamp'), datetime):
                message_copy['timestamp'] = message_copy['timestamp'].isoformat()
            if 'timestamp' not in message_copy:
                message_copy['timestamp'] = datetime.now().isoformat()
            if isinstance(message_copy.get('id'), ObjectId):
                message_copy['id'] = str(message_copy['id'])
            if 'metadata' in message_copy:
                if isinstance(message_copy['metadata'].get('chat_id'), ObjectId):
                    message_copy['metadata']['chat_id'] = str(message_copy['metadata']['chat_id'])
                if isinstance(message_copy['metadata'].get('user_id'), ObjectId):
                    message_copy['metadata']['user_id'] = str(message_copy['metadata']['user_id'])
            
            self.redis_client.rpush(session_key, json.dumps(message_copy))
            self.redis_client.expire(session_key, self.session_ttl)
            
            self.redis_client.ltrim(session_key, -50, -1)
            
            logger.debug(f"Stored message in Redis for session {session_key}")
            return follow_up
            
        except redis.ConnectionError as e:
            logger.error(f"Redis connection error during storage: {e}")
            return None
        except Exception as e:
            logger.error(f"Error storing short-term message: {e}")
            return None

    def store_session_metadata(self, user_id: str, chat_id: str, metadata: Dict):
        """
        Store session metadata in Redis
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
            metadata: Metadata dictionary
        """
        if not self.redis_client:
            logger.debug("Redis not available, skipping metadata storage")
            return
        
        try:
            user_id_obj = self._validate_object_id(user_id, "user_id")
            chat_id_obj = self._validate_object_id(chat_id, "chat_id")
            metadata_key = f"session_meta:{str(user_id_obj)}:{str(chat_id_obj)}"
            metadata_with_timestamp = metadata.copy()
            metadata_with_timestamp['updatedAt'] = datetime.now().isoformat()
            
            for key, value in metadata_with_timestamp.items():
                self.redis_client.hset(metadata_key, key, str(value))
            
            self.redis_client.expire(metadata_key, self.session_ttl)
            logger.debug(f"Stored session metadata in Redis for {metadata_key}")
            
        except Exception as e:
            logger.error(f"Error storing session metadata: {e}")

    def clear_session(self, user_id: str, chat_id: str):
        """
        Clear session data from Redis
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
        """
        if not self.redis_client:
            logger.debug("Redis not available, skipping session cleanup")
            return
        
        try:
            user_id_obj = self._validate_object_id(user_id, "user_id")
            chat_id_obj = self._validate_object_id(chat_id, "chat_id")
            session_key = f"session:{str(user_id_obj)}:{str(chat_id_obj)}"
            metadata_key = f"session_meta:{str(user_id_obj)}:{str(chat_id_obj)}"
            
            deleted_count = self.redis_client.delete(session_key, metadata_key)
            logger.info(f"Cleared {deleted_count} keys from Redis: {session_key} and {metadata_key}")
            
        except Exception as e:
            logger.error(f"Error clearing session: {e}")

    def get_memory_stats(self) -> Dict[str, Any]:
        """
        Get memory usage statistics

        Returns:
            Memory statistics dictionary
        """
        try:
            stats = {
                "redis_available": self.redis_client is not None,
                "mongodb_available": self.mongo_collection is not None,
                "session_ttl": self.session_ttl,
                "timestamp": datetime.now()
            }
        except Exception as e:
            # Fallback stats if there's an issue with collection comparison
            stats = {
                "redis_available": False,
                "mongodb_available": False,
                "session_ttl": self.session_ttl,
                "timestamp": datetime.now(),
                "error": str(e)
            }
        
        if self.redis_client:
            try:
                info = self.redis_client.info()
                stats["redis_stats"] = {
                    "used_memory": info.get("used_memory_human", "N/A"),
                    "connected_clients": info.get("connected_clients", "N/A"),
                    "total_commands_processed": info.get("total_commands_processed", "N/A")
                }
            except Exception as e:
                stats["redis_error"] = str(e)
        
        return stats

    def ensure_session_isolation(self, user_id: str, chat_id: str) -> bool:
        """
        Ensure session exists and is properly isolated
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
        
        Returns:
            True if session is valid and isolated
        """
        if not self.redis_client:
            return False
        
        try:
            user_id_obj = self._validate_object_id(user_id, "user_id")
            chat_id_obj = self._validate_object_id(chat_id, "chat_id")
            session_key = f"session:{str(user_id_obj)}:{str(chat_id_obj)}"
            
            exists = self.redis_client.exists(session_key)
            
            if not exists:
                session_metadata = {
                    "createdAt": datetime.now().isoformat(),
                    "user_id": str(user_id_obj),
                    "chat_id": str(chat_id_obj),
                    "message_count": 0,
                    "last_activity": datetime.now().isoformat()
                }
                self.store_session_metadata(str(user_id_obj), str(chat_id_obj), session_metadata)
                logger.info(f"Initialized new isolated session: {session_key}")
            
            self.redis_client.hset(f"session_meta:{str(user_id_obj)}:{str(chat_id_obj)}", 
                                  "last_activity", datetime.now().isoformat())
            
            return True
        
        except Exception as e:
            logger.error(f"Error ensuring session isolation: {e}")
            return False

    def get_session_specific_context(self, user_id: str, chat_id: str, limit: int = 15) -> List[Dict]:
        """
        Get context ONLY from the specific session - no cross-contamination
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
            limit: Maximum number of messages
        
        Returns:
            Session-specific context only
        """
        # Ensure session isolation first
        if not self.ensure_session_isolation(user_id, chat_id):
            return []
        
        try:
            user_id_obj = self._validate_object_id(user_id, "user_id")
            chat_id_obj = self._validate_object_id(chat_id, "chat_id")
            session_key = f"session:{str(user_id_obj)}:{str(chat_id_obj)}"
            
            messages = self.redis_client.lrange(session_key, -limit, -1)
            
            if messages:
                self.redis_client.expire(session_key, self.session_ttl)
                
                session_messages = []
                for msg in messages:
                    parsed_msg = json.loads(msg)
                    parsed_msg['id'] = ObjectId(parsed_msg['id']) if parsed_msg.get('id') else parsed_msg['id']
                    parsed_msg['timestamp'] = datetime.fromisoformat(parsed_msg['timestamp']) if parsed_msg.get('timestamp') else parsed_msg['timestamp']
                    msg_chat_id = parsed_msg.get('metadata', {}).get('chat_id')
                    msg_user_id = parsed_msg.get('metadata', {}).get('user_id')
                    if (msg_chat_id == str(chat_id_obj) or msg_chat_id is None) and (msg_user_id == str(user_id_obj) or msg_user_id is None):
                        parsed_msg['metadata']['chat_id'] = ObjectId(msg_chat_id) if msg_chat_id else None
                        parsed_msg['metadata']['user_id'] = ObjectId(msg_user_id) if msg_user_id else None
                        session_messages.append(parsed_msg)
                    else:
                        logger.warning(f"Found message from different session in {session_key}: expected chat_id {chat_id}, user_id {user_id}, got chat_id {msg_chat_id}, user_id {msg_user_id}")
            
                logger.debug(f"Retrieved {len(session_messages)} session-specific messages")
                return session_messages
        
            return []
        
        except Exception as e:
            logger.error(f"Error getting session-specific context: {e}")
            return []
        
    def store_session_message_isolated(self, user_id: str, chat_id: str, message: Dict):
        """
        Store message with strict session isolation
        
        Args:
            user_id: User identifier
            chat_id: Chat session identifier
            message: Message to store
        """
        if not self.redis_client:
            logger.debug("Redis not available")
            return
        
        try:
            user_id_obj = self._validate_object_id(user_id, "user_id")
            chat_id_obj = self._validate_object_id(chat_id, "chat_id")
            if not self.ensure_session_isolation(str(user_id_obj), str(chat_id_obj)):
                logger.error(f"Failed to ensure session isolation for {str(user_id_obj)}:{str(chat_id_obj)}")
                return
            
            session_key = f"session:{str(user_id_obj)}:{str(chat_id_obj)}"
            
            message_with_session = message.copy()
            if 'metadata' not in message_with_session:
                message_with_session['metadata'] = {}
            
            message_with_session['metadata'].update({
                'chat_id': str(chat_id_obj),
                'user_id': str(user_id_obj),
                'session_key': session_key,
                'stored_at': datetime.now().isoformat()
            })
            
            if isinstance(message_with_session.get('timestamp'), datetime):
                message_with_session['timestamp'] = message_with_session['timestamp'].isoformat()
            if 'timestamp' not in message_with_session:
                message_with_session['timestamp'] = datetime.now().isoformat()
            if isinstance(message_with_session.get('id'), ObjectId):
                message_with_session['id'] = str(message_with_session['id'])
            
            self.redis_client.rpush(session_key, json.dumps(message_with_session))
            self.redis_client.expire(session_key, self.session_ttl)
            
            self.redis_client.ltrim(session_key, -10, -1)
            
            self.redis_client.hincrby(f"session_meta:{str(user_id_obj)}:{str(chat_id_obj)}", "message_count", 1)
            self.redis_client.hset(f"session_meta:{str(user_id_obj)}:{str(chat_id_obj)}", 
                                  "last_activity", datetime.now().isoformat())
            
            logger.debug(f"Stored isolated message in session {session_key}")
            
        except Exception as e:
            logger.error(f"Error storing isolated session message: {e}")

# Global memory manager instance
memory_manager = None

def get_memory_manager(redis_client=None, mongo_client=None, mongo_db=None,
                      mongo_collection=None) -> MemoryManager:
    """Get or create memory manager instance"""
    global memory_manager
    if memory_manager is None:
        memory_manager = MemoryManager(redis_client, mongo_client, mongo_db, mongo_collection)
    else:
        # Update connections if provided
        if redis_client is not None:
            memory_manager.redis_client = redis_client
        if mongo_collection is not None:
            memory_manager.mongo_collection = mongo_collection
    return memory_manager

if __name__ == "__main__":
    # Test the memory manager
    print("🧠 Memory Manager Test")
    print("=" * 50)
    
    # Create test instance
    manager = get_memory_manager()
    
    # Test memory stats
    stats = manager.get_memory_stats()
    print(f"Redis available: {stats['redis_available']}")
    print(f"MongoDB available: {stats['mongodb_available']}")
    print(f"Session TTL: {stats['session_ttl']} seconds")
    
    print("Memory Manager test completed")