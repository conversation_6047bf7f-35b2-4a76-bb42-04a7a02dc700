# LLM-Based Suggested Questions Implementation

## Overview

I have successfully updated the suggested questions feature to **extract 3 suggested follow-up questions directly from the LLM response** instead of using hardcoded questions. This implementation follows the same pattern as the main follow-up question extraction (using 👉) but adds a new pattern for suggested questions.

## Key Changes

### ✅ **LLM Prompt Enhancement**
- Updated the LLM prompt to generate 3 suggested questions in addition to the main follow-up
- Added new pattern: `🔍 SUGGESTED QUESTIONS:` followed by numbered questions
- Questions are generated by the LLM based on the query and context

### ✅ **New Extraction Methods**
- `extract_suggested_questions()` - Extracts suggested questions using 🔍 pattern
- `extract_follow_up_and_suggestions()` - Extracts both main follow-up and suggested questions
- `extract_suggested_questions_from_response()` - Helper function for response processing

### ✅ **Updated Response Processing**
- Both `/query` and `/rag_query` endpoints now extract suggested questions from LLM responses
- Fallback to topic-based questions if extraction fails
- Maintains the same response format for users

## Implementation Details

### 1. **Enhanced LLM Prompt**

The LLM now generates responses in this format:
```
[Main Answer Content]

👉 Would you like me to explain more about [specific aspect]?

🔍 SUGGESTED QUESTIONS:
1. What are the main types of [topic]?
2. How do I get started with [topic]?
3. What are real-world applications of [topic]?
```

### 2. **Extraction Pattern**

#### **Suggested Questions Pattern:**
```python
pattern = r'🔍\s*SUGGESTED\s*QUESTIONS:\s*\n((?:\d+\.\s*.+\n?)+)'
```

#### **Individual Question Extraction:**
```python
question_pattern = r'(\d+)\.\s*(.+?)(?=\n\d+\.|$)'
```

### 3. **New Functions Added**

#### `extract_suggested_questions(response_text: str) -> tuple[str, List[str]]`
```python
# Extracts suggested questions from LLM response
# Returns: (cleaned_answer, suggested_questions_list)
```

#### `extract_follow_up_and_suggestions(response_text: str) -> tuple[str, Optional[str], List[str]]`
```python
# Extracts both main follow-up and suggested questions
# Returns: (cleaned_answer, follow_up_question, suggested_questions_list)
```

#### `extract_suggested_questions_from_response(full_response: str) -> List[str]`
```python
# Helper function that ensures exactly 3 questions are returned
# Falls back to topic-based questions if needed
```

### 4. **Updated Response Processing**

#### **Before (Hardcoded):**
```python
# Generate suggested questions using hardcoded templates
suggested_questions = generate_topic_based_questions(query, answer)
```

#### **After (LLM Extracted):**
```python
# Extract from LLM response first
final_answer, follow_up, suggested_questions_from_llm = memory_manager2.extract_follow_up_and_suggestions(full_response)

if suggested_questions_from_llm:
    # Use LLM-generated questions
    display_suggested_questions(suggested_questions_from_llm)
else:
    # Fallback to topic-based questions
    fallback_questions = generate_suggested_questions(query, full_response, host, model, api_key)
    display_suggested_questions(fallback_questions)
```

## Response Flow

### **Step-by-Step Process:**

1. **User Query**: "What is machine learning?"

2. **LLM Response Generation**: 
   ```
   Machine learning is a subset of AI that enables computers to learn from data...
   
   👉 Would you like me to explain more about the different types of machine learning algorithms?
   
   🔍 SUGGESTED QUESTIONS:
   1. What are the main types of machine learning?
   2. How do I get started with machine learning?
   3. What are popular machine learning frameworks?
   ```

3. **Extraction Process**:
   - Extract main answer (removes 👉 and 🔍 sections)
   - Extract main follow-up: "Would you like me to explain more about..."
   - Extract suggested questions: ["What are the main types...", "How do I get started...", "What are popular..."]

4. **Response Display**:
   ```
   Machine learning is a subset of AI that enables computers to learn from data...
   
   Source: qdrant
   Documents:
     1. File name: ml_guide.pdf
        Collection name: Gen AI
   
   Chat ID: 64fa2c8b9a1d3b0a12345678
   
   Suggested Questions:
   1. What are the main types of machine learning?
   2. How do I get started with machine learning?
   3. What are popular machine learning frameworks?
   ```

## Fallback System

### **Multi-Level Fallback:**

1. **Primary**: Extract from LLM response using 🔍 pattern
2. **Secondary**: If extraction fails, use topic-based generation
3. **Tertiary**: If topic detection fails, use default questions

### **Fallback Logic:**
```python
try:
    # Try LLM extraction first
    suggested_questions = extract_suggested_questions_from_response(llm_response)
    if suggested_questions:
        return suggested_questions
except:
    pass

try:
    # Fallback to topic-based
    return generate_topic_based_questions(query, answer)
except:
    # Final fallback to defaults
    return ["Can you explain this in more detail?", "What are some practical examples?", "How does this relate to other concepts?"]
```

## Benefits

### ✅ **Dynamic and Contextual**
- Questions are generated by the LLM based on actual content
- More relevant and specific to the user's query
- Adapts to different topics and contexts automatically

### ✅ **Consistent with Existing Pattern**
- Uses the same extraction approach as main follow-up questions
- Maintains consistency in the codebase
- Easy to understand and maintain

### ✅ **Reliable Fallback**
- Multiple fallback levels ensure questions are always displayed
- Graceful degradation when LLM doesn't generate suggestions
- No breaking changes to existing functionality

### ✅ **Enhanced User Experience**
- More intelligent and relevant question suggestions
- Better conversation flow and topic exploration
- Encourages deeper engagement with the content

## Testing

### **Test Coverage:**
1. **LLM Response Extraction**: Tests pattern matching and question extraction
2. **Fallback Scenarios**: Tests when LLM doesn't generate suggestions
3. **Response Structure**: Verifies correct placement after Chat ID
4. **Both Endpoints**: Tests `/query` and `/rag_query` endpoints
5. **Edge Cases**: Empty responses, malformed patterns, etc.

### **Test Endpoint:**
- **URL**: `GET /test_suggested_questions`
- **Purpose**: Comprehensive testing of extraction functionality
- **Coverage**: Mock LLM responses with and without suggested questions

## Configuration

### **LLM Prompt Requirements:**
- Must include instruction to generate suggested questions
- Must use the `🔍 SUGGESTED QUESTIONS:` pattern
- Must number questions as 1., 2., 3.
- Questions should be concise (max 10-12 words)

### **Extraction Settings:**
- **Pattern**: Case-insensitive matching
- **Question Count**: Exactly 3 questions expected
- **Format**: Numbered list with periods
- **Cleanup**: Automatic removal from main answer

## Migration Notes

### **Backward Compatibility:**
- ✅ Existing functionality preserved
- ✅ Same response format for users
- ✅ Fallback ensures questions always appear
- ✅ No breaking changes to API

### **Performance Impact:**
- **Minimal**: Extraction is regex-based (fast)
- **No Additional LLM Calls**: Uses existing response
- **Efficient**: Single extraction for both follow-up and suggestions

The implementation successfully transforms the suggested questions feature from hardcoded templates to dynamic, LLM-generated questions that are contextually relevant and extracted using the same reliable pattern as the main follow-up questions.
